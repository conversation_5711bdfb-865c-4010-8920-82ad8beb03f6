import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const filePath = searchParams.get("path");

    if (!filePath) {
      return NextResponse.json(
        { error: "Path parameter is required" },
        { status: 400 }
      );
    }

    // 安全检查：确保路径不会访问项目外的文件
    const fullPath = path.join(process.cwd(), "output", filePath);
    const normalizedPath = path.normalize(fullPath);
    const outputDir = path.join(process.cwd(), "output");

    if (!normalizedPath.startsWith(outputDir)) {
      return NextResponse.json(
        { error: "Invalid path" },
        { status: 403 }
      );
    }

    const content = await fs.readFile(normalizedPath, "utf-8");

    return NextResponse.json({ content });
  } catch (error) {
    console.error("读取文件失败:", error);
    return NextResponse.json(
      { error: "Failed to read file" },
      { status: 500 }
    );
  }
}

