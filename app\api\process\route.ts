import { NextRequest } from "next/server";
import fs from "fs/promises";
import path from "path";
import { processWithGemini } from "@/lib/gemini-client";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

interface ProcessRequest {
  files: string[];
  rule: string;
}

// 安全的配置解析函数
function parseRuleConfig(content: string): any {
  try {
    // 移除注释
    const cleanContent = content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');

    // 更精确的匹配，处理多行对象
    const configMatch = cleanContent.match(/export\s+const\s+(\S+Config)\s*=\s*(\{[\s\S]*?\})\s*;?\s*$/);

    if (!configMatch) {
      throw new Error("No config export found");
    }

    let configObject = configMatch[2];

    // 确保对象完整
    if (!configObject.trim().endsWith('}')) {
      configObject += '}';
    }

    try {
      // 首先尝试作为 JSON 解析（处理带引号的格式）
      const config = JSON.parse(configObject);
      return config;
    } catch (jsonError) {
      // 如果 JSON 解析失败，尝试使用 Function 构造器（处理 JS 对象格式）
      try {
        const parseFunction = new Function('return ' + configObject);
        const config = parseFunction();
        return config;
      } catch (funcError) {
        console.error("Both JSON and Function parsing failed:", { jsonError: jsonError.message, funcError: funcError.message });
        return null;
      }
    }
  } catch (error) {
    console.error("Config parsing error:", error);
    return null;
  }
}

export async function POST(request: NextRequest) {
  const encoder = new TextEncoder();

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const body: ProcessRequest = await request.json();
        const { files, rule } = body;

        // 加载规则配置
        const rulePath = path.join(process.cwd(), "rules", `${rule}.js`);
        let ruleConfig;

        try {
          console.log("Loading rule from path:", rulePath);
          const ruleContent = await fs.readFile(rulePath, "utf-8");
          console.log("File content loaded, length:", ruleContent.length);

          const config = parseRuleConfig(ruleContent);

          if (config) {
            console.log("Successfully parsed config:", config.model);
            ruleConfig = config;
          } else {
            throw new Error("Could not parse rule configuration");
          }
        } catch (error) {
          console.error("加载规则失败:", error);

          // 如果规则文件不存在或解析失败，使用默认配置
          console.log(`使用默认配置替代规则: ${rule}`);
          ruleConfig = {
            model: "gemini-2.0-flash-exp",
            config: {
              systemInstruction: `你是一个专业的内容生成助手。`,
            }
          };
        }

        const total = files.length;
        let completed = 0;

        // 并发处理文件（最多 3 个并发）
        const maxConcurrent = 3;
        const queue = [...files];
        const workers: Promise<void>[] = [];

        for (let i = 0; i < Math.min(maxConcurrent, files.length); i++) {
          workers.push(
            (async () => {
              while (queue.length > 0) {
                const filePath = queue.shift();
                if (!filePath) break;

                try {
                  // 读取输入文件
                  const inputPath = path.join(process.cwd(), "input", filePath);
                  const content = await fs.readFile(inputPath, "utf-8");

                  // 发送处理开始事件
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({
                        type: "progress",
                        fileName: path.basename(filePath),
                        status: "processing",
                        completed,
                        total,
                        originalContent: content,
                        rule: rule,
                        message: `正在使用 ${ruleConfig.model} 处理文件...`,
                      })}\n\n`
                    )
                  );

                  // 使用 Gemini 处理
                  const result = await processWithGemini(
                    content,
                    ruleConfig,
                    filePath
                  );

                  if (result.success && result.content) {
                    // 按规则分类保存到输出文件夹
                    const outputPath = path.join(process.cwd(), "output", rule, filePath);
                    const outputDir = path.dirname(outputPath);

                    // 确保输出目录存在
                    await fs.mkdir(outputDir, { recursive: true });

                    // 写入文件
                    await fs.writeFile(outputPath, result.content, "utf-8");

                    completed++;

                    // 发送进度更新
                    controller.enqueue(
                      encoder.encode(
                        `data: ${JSON.stringify({
                          type: "progress",
                          fileName: path.basename(filePath),
                          status: "completed",
                          completed,
                          total,
                          generatedContent: result.content,
                          rule: rule,
                          outputPath: path.join(rule, filePath),
                          message: `文件处理完成，已保存到 ${rule}/${filePath}`,
                        })}\n\n`
                      )
                    );
                  } else {
                    // 处理失败
                    controller.enqueue(
                      encoder.encode(
                        `data: ${JSON.stringify({
                          type: "progress",
                          fileName: path.basename(filePath),
                          status: "error",
                          completed,
                          total,
                          error: result.error || "处理失败",
                          rule: rule,
                          message: `文件处理失败: ${result.error || "未知错误"}`,
                        })}\n\n`
                      )
                    );
                  }
                } catch (error: any) {
                  console.error(`处理文件 ${filePath} 失败:`, error);
                  controller.enqueue(
                    encoder.encode(
                      `data: ${JSON.stringify({
                        type: "progress",
                        fileName: path.basename(filePath),
                        status: "error",
                        completed,
                        total,
                        error: error.message,
                        rule: rule,
                        message: `处理文件时发生错误: ${error.message}`,
                      })}\n\n`
                    )
                  );
                }
              }
            })()
          );
        }

        // 等待所有工作完成
        await Promise.all(workers);

        // 发送完成事件
        controller.enqueue(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "complete",
              completed,
              total,
            })}\n\n`
          )
        );

        controller.close();
      } catch (error: any) {
        console.error("处理请求失败:", error);
        controller.enqueue(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "error",
              message: error.message,
            })}\n\n`
          )
        );
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      "Connection": "keep-alive",
    },
  });
}

