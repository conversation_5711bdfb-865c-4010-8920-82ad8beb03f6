# Learning Studio - AI 内容生成器

一个基于 Next.js 和 Gemini AI 的全栈应用，用于转换和增强学习材料。

## 功能特性

### 📁 文件处理系统
- **双文件树视图**：同时显示输入和输出文件夹
- **批量选择**：可勾选多个文件进行批量处理
- **实时进度**：显示处理进度和状态
- **文件预览**：点击已完成的文件可预览 Markdown 内容

### 🔑 智能 API Key 管理
- **负载均衡**：自动在多个 API keys 之间分配请求
- **速率限制**：跟踪每个 key 的使用情况，避免超限
- **错误处理**：自动重试和 key 降级
- **并发控制**：支持多个文件并发处理

### 🎨 Studio 工作室
- **多种模板**：
  - Audio Overview - 音频概述
  - Video Overview - 视频概述
  - Mind Map - 思维导图
  - Reports - 报告
  - Flashcards - 闪卡
  - Quiz - 测验
- **规则编辑**：实时编辑和保存规则配置
- **可扩展**：轻松添加新的内容生成模板

### 🤖 Gemini AI 集成
- **并发处理**：同时处理多个文件
- **错误重试**：自动重试失败的请求
- **流式响应**：实时显示处理进度

## 技术栈

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **UI 库**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **AI**: Google Gemini API
- **Markdown**: react-markdown

## 快速开始

### 1. 安装依赖

\`\`\`bash
npm install
\`\`\`

### 2. 配置 API Keys

API keys 已在 `.env` 文件中配置。如需修改，请编辑 `lib/api-key-manager.ts`。

### 3. 准备输入文件

将要处理的 Markdown 文件放入 `input` 文件夹。支持嵌套目录结构。

### 4. 启动开发服务器

\`\`\`bash
npm run dev
\`\`\`

访问 [http://localhost:3000](http://localhost:3000)

## 使用指南

### 文件处理

1. 在"文件处理"标签页，左侧显示输入文件树
2. 勾选要处理的文件
3. 点击"开始处理"按钮
4. 等待处理完成，右侧会显示输出文件
5. 点击已完成的文件可预览内容

### Studio 工作室

1. 在"工作室"标签页，选择一个模板
2. 点击编辑按钮可修改规则配置
3. 规则配置使用 JSON 格式
4. 保存后即可在文件处理中使用

## 项目结构

\`\`\`
learning-studio/
├── app/                    # Next.js App Router
│   ├── api/               # API 路由
│   │   ├── files/        # 文件系统 API
│   │   ├── process/      # 处理 API
│   │   └── rules/        # 规则管理 API
│   ├── globals.css       # 全局样式
│   ├── layout.tsx        # 根布局
│   └── page.tsx          # 主页面
├── components/            # React 组件
│   ├── ui/               # UI 基础组件
│   ├── file-tree.tsx     # 文件树组件
│   ├── file-processor.tsx # 文件处理器
│   └── studio.tsx        # 工作室组件
├── lib/                   # 工具库
│   ├── api-key-manager.ts # API Key 管理器
│   ├── gemini-client.ts   # Gemini 客户端
│   └── utils.ts          # 工具函数
├── input/                 # 输入文件夹
├── output/                # 输出文件夹
├── rules/                 # 规则配置
│   ├── revise.js         # 修订规则
│   └── anki.js           # Anki 规则
└── .env                   # 环境变量（API keys）
\`\`\`

## API Key 管理

系统会自动管理多个 API keys：

- **轮询分配**：优先使用请求次数最少的 key
- **速率限制**：每个 key 每分钟最多 15 个请求
- **错误处理**：连续 3 次错误后自动禁用该 key
- **自动恢复**：冷却期后自动恢复可用状态

## 自定义规则

规则文件位于 `rules/` 目录，格式如下：

\`\`\`javascript
export const myRuleConfig = {
  model: "gemini-2.0-flash-exp",
  config: {
    thinkingConfig: {
      thinkingBudget: 32000,
    },
    tools: [{ googleSearch: {} }],
    systemInstruction: \`你的系统指令...\`,
  }
};
\`\`\`

## 注意事项

1. **API 限制**：注意 Gemini API 的速率限制
2. **文件大小**：大文件可能需要更长的处理时间
3. **并发数**：默认最多 3 个并发请求，可在代码中调整
4. **错误处理**：处理失败的文件会在控制台显示错误信息

## 后续改进

- [ ] 添加更多内容生成模板
- [ ] 支持音频生成（TTS 集成）
- [ ] 添加数据库存储处理历史
- [ ] 支持更多文件格式
- [ ] 添加用户认证
- [ ] 优化大文件处理性能

## License

MIT

