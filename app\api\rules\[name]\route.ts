import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

// 安全的配置解析函数
function parseRuleConfig(content: string): any {
  try {
    // 移除注释
    const cleanContent = content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');

    // 更精确的匹配，处理多行对象
    const configMatch = cleanContent.match(/export\s+const\s+(\S+Config)\s*=\s*(\{[\s\S]*?\})\s*;?\s*$/);

    if (!configMatch) {
      throw new Error("No config export found");
    }

    let configObject = configMatch[2];

    // 确保对象完整
    if (!configObject.trim().endsWith('}')) {
      configObject += '}';
    }

    try {
      // 首先尝试作为 JSON 解析（处理带引号的格式）
      const config = JSON.parse(configObject);
      return config;
    } catch (jsonError) {
      // 如果 JSON 解析失败，尝试使用 Function 构造器（处理 JS 对象格式）
      try {
        const parseFunction = new Function('return ' + configObject);
        const config = parseFunction();
        return config;
      } catch (funcError) {
        console.error("Both JSON and Function parsing failed:", { jsonError: jsonError.message, funcError: funcError.message });
        return null;
      }
    }
  } catch (error) {
    console.error("Config parsing error:", error);
    return null;
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    try {
      console.log("Loading rule from path:", rulePath);
      const content = await fs.readFile(rulePath, "utf-8");
      console.log("File content loaded, length:", content.length);

      const config = parseRuleConfig(content);

      if (config) {
        console.log("Successfully parsed config:", config.model);
        return NextResponse.json(config);
      } else {
        console.log("Failed to parse config, using default");
        throw new Error("Could not parse rule configuration");
      }
    } catch (error) {
      console.error("Rule loading error:", error.message);
      // 如果文件不存在，返回默认配置
      return NextResponse.json({
        model: "gemini-2.0-flash-exp",
        config: {
          systemInstruction: `你是一个专业的内容生成助手。`,
        }
      });
    }
  } catch (error) {
    console.error("获取规则失败:", error);
    return NextResponse.json(
      { error: "Failed to get rule" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ name: string }> }
) {
  try {
    const { name } = await params;
    const config = await request.json();

    const rulePath = path.join(process.cwd(), "rules", `${name}.js`);

    // 生成安全的变量名（处理中文和特殊字符）
    const safeVarName = name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_');

    // 将配置转换为 JS 文件格式
    const content = `export const ${safeVarName}Config = ${JSON.stringify(config, null, 2)};
`;

    await fs.writeFile(rulePath, content, "utf-8");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("保存规则失败:", error);
    return NextResponse.json(
      { error: "Failed to save rule" },
      { status: 500 }
    );
  }
}

