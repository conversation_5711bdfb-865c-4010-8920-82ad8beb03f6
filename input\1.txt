在HTTP方法的设计中，“安全性 (Safety)”指的是什么？	<b>直接答案</b>：执行该方法<b>不应该改变服务器上的资源状态</b>。<br><br><b>解释</b>：安全的HTTP方法本质上是“只读”操作。无论调用多少次，服务器上的数据都不会被修改、创建或删除。<br><br><b>类比</b>：在图书馆的查询系统里搜索一本书。无论搜索多少次，图书馆的藏书状态都不会改变，因此“搜索”操作是安全的。	WebDev::HTTP::Concepts::Safety
在HTTP方法的设计中，“幂等性 (Idempotency)”指的是什么？	<b>直接答案</b>：<b>多次执行同样的请求</b>，其对服务器资源状态的最终影响，与<b>执行一次请求</b>的影响是完全相同的。<br><br><b>解释</b>：幂等性关注的是操作的最终结果，而不是过程。第一次请求可能会改变状态，但后续所有相同的请求都不会再产生新的变化。<br><br><b>类比</b>：将房间空调温度设置为20°C。按一次遥控器，目标是20°C；连续按十次，目标依然是20°C。这个“设置温度”的操作就是幂等的。	WebDev::HTTP::Concepts::Idempotency
根据“安全性”和“幂等性”原则，GET方法被设计成什么样的？	<b>既安全 (Safe) 又幂等 (Idempotent)</b>。<br><br><b>解释</b>：GET的核心语义是“获取”或“检索”资源，这个操作不应该有任何副作用（Side Effect），且多次获取同一资源的结果应相同（不考虑资源本身被其他方式修改）。	WebDev::HTTP::Methods::GET-vs-POST
根据“安全性”和“幂等性”原则，POST方法被设计成什么样的？	<b>既不安全 (Unsafe) 也不幂等 (Non-idempotent)</b>。<br><br><b>解释</b>：POST的核心语义是“提交”或“处理”数据，这通常会导致服务器状态的改变（如创建新资源）。每次执行POST请求都可能产生一个新的、独立的结果（例如，连续提交两次订单会创建两个订单）。	WebDev::HTTP::Methods::GET-vs-POST
GET请求的参数通常放在哪里，并且有什么格式限制？	<b>位置</b>：作为<b>查询字符串 (Query String)</b> 附加在URL的末尾。<br><br><b>格式</b>：采用 `key=value&key2=value2` 的形式，并且只能包含ASCII字符。非ASCII字符需要进行URL编码。<br><br><b>示例</b>：`https://api.example.com/users?status=active&sortBy=name`	WebDev::HTTP::Methods::GET-vs-POST
POST请求的参数通常放在哪里，并且支持哪些数据格式？	<b>位置</b>：放在<b>请求体 (Request Body)</b> 中。<br><br><b>支持格式</b>：支持多种数据格式，通过 `Content-Type` 头部来指定。常见格式包括：<ul><li>`application/json` (用于API通信)</li><li>`multipart/form-data` (用于文件上传)</li><li>`application/x-www-form-urlencoded` (传统的HTML表单提交)</li></ul>	WebDev::HTTP::Methods::GET-vs-POST
为什么GET请求可以被浏览器和代理服务器缓存，而POST请求默认不能？	<b>GET的可缓存性</b>：因为GET被设计为<b>安全且幂等</b>的，它只是获取数据而不改变服务器状态。因此，重复请求相同URL的GET，可以直接从缓存返回响应，以提升性能。<br><br><b>POST的不可缓存性</b>：因为POST是<b>不安全且不幂等</b>的，每次请求都可能导致服务器状态发生变化（如创建新数据）。缓存POST的响应是危险的，可能会导致意外的重复操作。	WebDev::HTTP::Methods::GET-vs-POST
GET和POST请求对浏览器历史记录和URL可见性有何不同影响？	<b>GET</b>：请求参数直接暴露在URL中，因此会<b>被保存在浏览器历史记录</b>、服务器日志和网络嗅探器中，可见性高。<br><br><b>POST</b>：请求参数位于请求体中，<b>不会出现在URL里</b>，因此不会被保存在浏览器历史记录的URL部分，对用户来说是不可见的。	WebDev::HTTP::Methods::GET-vs-POST
GET和POST在传输数据量上有什么限制？	<b>GET</b>：存在<b>长度限制</b>。这并非HTTP协议的规定，而是由<b>浏览器和服务器对URL总长度的实际限制</b>所致，通常在2KB到8KB之间。<br><br><b>POST</b>：<b>理论上无限制</b>。数据大小主要由服务器的配置（如`max_post_size`）决定，远大于URL的长度限制。	WebDev::HTTP::Methods::GET-vs-POST
如何纠正“GET不安全，POST安全”这一常见误区？	这个说法的“不安全”指的是<b>数据保密性 (Confidentiality)</b>，而非<b>应用安全性 (Security)</b>。<br><br><ul><li><b>GET的保密性差</b>：将敏感信息（如密码）放在URL中，会使其暴露在浏览器历史、日志等多个地方，存在数据泄露风险。</li><li><b>POST本身不提供加密</b>：若不使用HTTPS，POST请求体中的数据在传输过程中仍是明文，可被截获。</li></ul><b>结论</b>：真正的Web安全依赖于<b>HTTPS加密</b>、认证、授权等综合措施，而不仅仅是选择POST方法。	WebDev::HTTP::Security::Confidentiality
在Web开发中，应该在什么场景下优先选择使用GET方法？	<b>核心原则</b>：当操作的目的是<b>获取或检索资源</b>，并且该操作<b>不会改变服务器状态</b>时。<br><br><b>具体场景</b>：<ul><li>读取数据（如获取文章列表、用户信息）</li><li>搜索</li><li>筛选与排序</li><li>分页查询</li></ul>	WebDev::HTTP::Methods::Best-Practices
在Web开发中，应该在什么场景下优先选择使用POST方法？	<b>核心原则</b>：当需要向服务器<b>提交数据</b>，并期望该操作<b>导致服务器状态发生改变</b>时。<br><br><b>具体场景</b>：<ul><li>创建新资源（如用户注册、发布文章）</li><li>更新现有资源（虽然PUT/PATCH更语义化，但POST也常用）</li><li>执行一个会产生副作用的操作（如提交订单、发送消息）</li><li>上传文件</li></ul>	WebDev::HTTP::Methods::Best-Practices
当使用 `fetch` API 发送 `FormData` 对象（用于文件上传）时，为什么不应该手动设置 `Content-Type` 请求头？	因为浏览器会自动设置正确的 `Content-Type` 为 `multipart/form-data`，并且会附加一个必要的 `boundary` 参数。<br><br><b>解释</b>：`boundary` 是一个随机字符串，用于在请求体中分隔不同的表单字段和文件。如果手动设置 `Content-Type` 而忽略了 `boundary`，服务器将无法正确解析请求体，导致上传失败。	WebDev::HTTP::Methods::POST::FormData
一个HTTP方法是“{{c1::安全 (Safety)}}”的，指的是执行该方法不应该改变服务器上的资源状态，它是一个只读操作。	一个HTTP方法是“{{c1::安全 (Safety)}}”的，指的是执行该方法不应该改变服务器上的资源状态，它是一个只读操作。	WebDev::HTTP::Concepts::Safety::Definition
一个HTTP方法是“{{c1::幂等 (Idempotency)}}”的，指的是多次执行同样的请求，其对服务器资源状态的最终影响，与执行一次请求的影响是完全相同的。	一个HTTP方法是“{{c1::幂等 (Idempotency)}}”的，指的是多次执行同样的请求，其对服务器资源状态的最终影响，与执行一次请求的影响是完全相同的。	WebDev::HTTP::Concepts::Idempotency::Definition
选择HTTP方法时，应首先遵循其语义：使用 {{c1::GET}} 来<b>获取</b>资源，使用 {{c2::POST}} 来<b>提交</b>数据并期望服务器发生<b>状态改变</b>。	选择HTTP方法时，应首先遵循其语义：使用 {{c1::GET}} 来<b>获取</b>资源，使用 {{c2::POST}} 来<b>提交</b>数据并期望服务器发生<b>状态改变</b>。	WebDev::HTTP::Methods::Best-Practices