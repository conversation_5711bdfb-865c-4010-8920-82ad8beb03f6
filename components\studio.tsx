"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { 
  AudioWaveform, 
  Video, 
  Network, 
  FileText, 
  CreditCard, 
  HelpCircle,
  Edit,
  Save
} from "lucide-react";
import { cn } from "@/lib/utils";

interface StudioTemplate {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  rule?: string;
}

const templates: StudioTemplate[] = [
  {
    id: "audio",
    name: "Audio Overview",
    icon: <AudioWaveform className="h-6 w-6" />,
    color: "bg-purple-100 dark:bg-purple-900/20 text-purple-600 dark:text-purple-400",
    description: "生成音频概述内容",
    rule: "audio-overview",
  },
  {
    id: "video",
    name: "Video Overview",
    icon: <Video className="h-6 w-6" />,
    color: "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400",
    description: "生成视频概述内容",
    rule: "video-overview",
  },
  {
    id: "mindmap",
    name: "Mind Map",
    icon: <Network className="h-6 w-6" />,
    color: "bg-pink-100 dark:bg-pink-900/20 text-pink-600 dark:text-pink-400",
    description: "生成思维导图",
    rule: "mindmap",
  },
  {
    id: "reports",
    name: "Reports",
    icon: <FileText className="h-6 w-6" />,
    color: "bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400",
    description: "生成报告",
    rule: "reports",
  },
  {
    id: "flashcards",
    name: "Flashcards",
    icon: <CreditCard className="h-6 w-6" />,
    color: "bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400",
    description: "生成闪卡",
    rule: "anki",
  },
  {
    id: "quiz",
    name: "Quiz",
    icon: <HelpCircle className="h-6 w-6" />,
    color: "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400",
    description: "生成测验",
    rule: "quiz",
  },
];

export function Studio() {
  const [selectedTemplate, setSelectedTemplate] = useState<StudioTemplate | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [ruleContent, setRuleContent] = useState("");

  useEffect(() => {
    if (selectedTemplate?.rule) {
      loadRule(selectedTemplate.rule);
    }
  }, [selectedTemplate]);

  const loadRule = async (ruleName: string) => {
    try {
      const response = await fetch(`/api/rules/${ruleName}`);
      if (response.ok) {
        const data = await response.json();
        setRuleContent(JSON.stringify(data, null, 2));
      } else {
        // 如果规则不存在，创建默认规则
        setRuleContent(JSON.stringify({
          model: "gemini-2.0-flash-exp",
          config: {
            systemInstruction: `你是一个专业的内容生成助手，专门用于生成 ${selectedTemplate?.name} 内容。`,
          }
        }, null, 2));
      }
    } catch (error) {
      console.error("加载规则失败:", error);
    }
  };

  const handleSaveRule = async () => {
    if (!selectedTemplate?.rule) return;

    try {
      const ruleData = JSON.parse(ruleContent);
      const response = await fetch(`/api/rules/${selectedTemplate.rule}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(ruleData),
      });

      if (response.ok) {
        alert("规则保存成功！");
        setIsEditing(false);
      } else {
        alert("保存失败");
      }
    } catch (error) {
      console.error("保存规则失败:", error);
      alert("保存失败：JSON 格式错误");
    }
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => (
          <Card
            key={template.id}
            className="cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => setSelectedTemplate(template)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={cn("p-3 rounded-lg", template.color)}>
                  {template.icon}
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedTemplate(template);
                    setIsEditing(true);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
              <CardTitle className="mt-4">{template.name}</CardTitle>
              <CardDescription>{template.description}</CardDescription>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* 规则编辑对话框 */}
      <Dialog 
        open={!!selectedTemplate} 
        onOpenChange={() => {
          setSelectedTemplate(null);
          setIsEditing(false);
        }}
      >
        <DialogContent className="max-w-3xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedTemplate && (
                <div className={cn("p-2 rounded-lg", selectedTemplate.color)}>
                  {selectedTemplate.icon}
                </div>
              )}
              {selectedTemplate?.name}
            </DialogTitle>
            <DialogDescription>
              {isEditing ? "编辑规则配置" : "查看规则配置"}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {isEditing ? (
              <>
                <textarea
                  className="w-full h-96 p-4 font-mono text-sm border rounded-md bg-slate-50 dark:bg-slate-900"
                  value={ruleContent}
                  onChange={(e) => setRuleContent(e.target.value)}
                />
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    取消
                  </Button>
                  <Button onClick={handleSaveRule}>
                    <Save className="mr-2 h-4 w-4" />
                    保存规则
                  </Button>
                </div>
              </>
            ) : (
              <>
                <pre className="w-full h-96 p-4 font-mono text-sm border rounded-md bg-slate-50 dark:bg-slate-900 overflow-auto">
                  {ruleContent}
                </pre>
                <div className="flex justify-end">
                  <Button onClick={() => setIsEditing(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    编辑规则
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

