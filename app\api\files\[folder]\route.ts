import { NextRequest, NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";
import { FileNode } from "@/components/file-tree";

async function buildFileTree(dirPath: string, basePath: string = ""): Promise<FileNode[]> {
  const nodes: FileNode[] = [];

  try {
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      const relativePath = path.join(basePath, entry.name);

      if (entry.isDirectory()) {
        const children = await buildFileTree(fullPath, relativePath);
        nodes.push({
          name: entry.name,
          path: relativePath,
          type: "directory",
          children,
        });
      } else if (entry.isFile() && entry.name.endsWith(".md")) {
        nodes.push({
          name: entry.name,
          path: relativePath,
          type: "file",
        });
      }
    }
  } catch (error) {
    console.error(`读取目录失败: ${dirPath}`, error);
  }

  return nodes.sort((a, b) => {
    if (a.type === b.type) return a.name.localeCompare(b.name);
    return a.type === "directory" ? -1 : 1;
  });
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ folder: string }> }
) {
  try {
    const { folder } = await params;
    
    if (folder !== "input" && folder !== "output") {
      return NextResponse.json(
        { error: "Invalid folder" },
        { status: 400 }
      );
    }

    const folderPath = path.join(process.cwd(), folder);
    
    // 确保文件夹存在
    try {
      await fs.access(folderPath);
    } catch {
      await fs.mkdir(folderPath, { recursive: true });
    }

    const files = await buildFileTree(folderPath);

    return NextResponse.json({ files });
  } catch (error) {
    console.error("获取文件树失败:", error);
    return NextResponse.json(
      { error: "Failed to get file tree" },
      { status: 500 }
    );
  }
}

