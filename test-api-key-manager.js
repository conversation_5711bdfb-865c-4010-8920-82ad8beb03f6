// 测试 API Key 管理器
// 运行: node test-api-key-manager.js

class ApiKeyManager {
  constructor(apiKeys) {
    this.keys = apiKeys.map(config => ({
      key: config.key,
      name: config.name,
      requestCount: 0,
      lastUsed: 0,
      isAvailable: true,
      errorCount: 0,
    }));
    this.maxRequestsPerMinute = 15;
    this.cooldownPeriod = 60000;
    this.maxErrors = 3;
  }

  getNextKey() {
    const now = Date.now();
    
    this.keys.forEach(keyUsage => {
      if (now - keyUsage.lastUsed > this.cooldownPeriod) {
        keyUsage.requestCount = 0;
        if (keyUsage.errorCount < this.maxErrors) {
          keyUsage.isAvailable = true;
        }
      }
    });

    const availableKeys = this.keys.filter(
      k => k.isAvailable && k.requestCount < this.maxRequestsPerMinute
    );

    if (availableKeys.length === 0) {
      console.warn('所有 API keys 都不可用或已达到速率限制');
      return null;
    }

    const selectedKey = availableKeys.reduce((prev, current) =>
      prev.requestCount < current.requestCount ? prev : current
    );

    selectedKey.requestCount++;
    selectedKey.lastUsed = now;

    console.log(`✓ 使用 API Key: ${selectedKey.name} (使用次数: ${selectedKey.requestCount}/${this.maxRequestsPerMinute})`);
    
    return selectedKey.key;
  }

  reportSuccess(key) {
    const keyUsage = this.keys.find(k => k.key === key);
    if (keyUsage) {
      keyUsage.errorCount = 0;
    }
  }

  reportError(key) {
    const keyUsage = this.keys.find(k => k.key === key);
    if (keyUsage) {
      keyUsage.errorCount++;
      console.warn(`⚠ API Key ${keyUsage.name} 错误次数: ${keyUsage.errorCount}/${this.maxErrors}`);
      
      if (keyUsage.errorCount >= this.maxErrors) {
        keyUsage.isAvailable = false;
        console.error(`✗ API Key ${keyUsage.name} 已被禁用（错误次数过多）`);
      }
    }
  }

  getStatus() {
    return this.keys.map(k => ({
      name: k.name,
      requestCount: k.requestCount,
      isAvailable: k.isAvailable,
      errorCount: k.errorCount,
    }));
  }
}

// 测试
console.log('=== API Key 管理器测试 ===\n');

const apiKeys = [
  { key: 'key1', name: 'My First Project' },
  { key: 'key2', name: 'ankibot' },
  { key: 'key3', name: 'Generative Language Client' },
  { key: 'key4', name: 'In The Novel' },
  { key: 'key5', name: 'chat' },
];

const manager = new ApiKeyManager(apiKeys);

console.log('测试 1: 获取多个 keys（应该轮询）');
for (let i = 0; i < 10; i++) {
  manager.getNextKey();
}

console.log('\n测试 2: 报告错误');
manager.reportError('key1');
manager.reportError('key1');
manager.reportError('key1');

console.log('\n测试 3: 尝试获取 key（key1 应该被禁用）');
for (let i = 0; i < 5; i++) {
  manager.getNextKey();
}

console.log('\n测试 4: 状态报告');
console.table(manager.getStatus());

console.log('\n✓ 测试完成！');

