import { NextResponse } from "next/server";
import fs from "fs/promises";
import path from "path";

export async function GET() {
  try {
    const rulesDir = path.join(process.cwd(), "rules");

    // 确保规则目录存在
    try {
      await fs.access(rulesDir);
    } catch {
      await fs.mkdir(rulesDir, { recursive: true });
    }

    const files = await fs.readdir(rulesDir);
    const jsFiles = files.filter(file => file.endsWith('.js'));

    const rules = [];

    for (const file of jsFiles) {
      const ruleName = file.replace('.js', '');
      const filePath = path.join(rulesDir, file);

      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const match = content.match(/export\s+const\s+[^\s=]+Config\s*=\s*(\{[\s\S]*?\});?\s*$/m);

        if (match) {
          // 简单提取模型信息和描述
          const modelMatch = content.match(/"model":\s*"([^"]+)"/);
          const systemInstructionMatch = content.match(/"systemInstruction":\s*"([^"]{0,100})/);

          rules.push({
            name: ruleName,
            displayName: ruleName,
            description: systemInstructionMatch ? systemInstructionMatch[1] + '...' : '自定义规则',
            model: modelMatch ? modelMatch[1] : 'gemini-2.0-flash-exp'
          });
        } else {
          rules.push({
            name: ruleName,
            displayName: ruleName,
            description: '未找到规则配置',
            model: 'unknown'
          });
        }
      } catch (error) {
        console.error(`解析规则文件 ${file} 失败:`, error);
        // 即使解析失败，也添加到列表中
        rules.push({
          name: ruleName,
          displayName: ruleName,
          description: '规则解析失败',
          model: 'unknown'
        });
      }
    }

    return NextResponse.json({ rules });
  } catch (error) {
    console.error("获取规则列表失败:", error);
    return NextResponse.json(
      { error: "Failed to get rules list" },
      { status: 500 }
    );
  }
}
